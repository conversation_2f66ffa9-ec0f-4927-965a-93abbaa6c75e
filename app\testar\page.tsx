import Link from "next/link";
import { urls } from "../_lib/constants";
import TestAuth from "./component/test-auth";
// Server component
export default async function TestarPage() {
	return (
		<div className="p-6 max-w-4xl mx-auto">
			<h1 className="text-3xl font-bold mb-6">Test page - Microsoft SSO</h1>

			{/* Microsoft SSO Authentication Component */}
			<div className="mb-8 p-6 border rounded-lg bg-gray-50">
				<h2 className="text-xl font-semibold mb-4">
					Microsoft SSO Authentication
				</h2>
				<TestAuth />
			</div>

			{/* Navigation Links */}
			<div className="mb-6">
				<h3 className="text-lg font-medium mb-3">Navigation</h3>
				<ul className="space-y-2">
					{urls.map((item) => (
						<li key={item.url}>
							<Link
								className="text-blue-600 hover:text-blue-800 underline"
								href={item.url}
							>
								{item.title}
							</Link>
						</li>
					))}
				</ul>
			</div>
		</div>
	);
}
