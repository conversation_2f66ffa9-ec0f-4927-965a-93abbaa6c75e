{"biome.enabled": true, "eslint.enable": true, "sort-imports.on-save": false, "editor.defaultFormatter": "biomejs.biome", "editor.formatOnPaste": false, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"quickfix.biome": "always", "source.organizeImports.biome": "always"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[css]": {"editor.defaultFormatter": "biomejs.biome"}, "[dockerfile]": {"editor.defaultFormatter": "ms-azuretools.vscode-docker"}, "files.associations": {"**/pipeline/*.yml": "azure-pipelines"}, "azure-pipelines.customSchemaFile": "https://dev.azure.com/regionskane-utv/_apis/distributedtask/yamlschema", "[yaml]": {"editor.defaultFormatter": "ms-azure-devops.azure-pipelines"}}