import type { CapabilityStatement } from "fhir/r4";
import FhirClient from "fhirclient/lib/FhirClient";
import Link from "next/link";
import { urls } from "../_lib/constants";
import { getFhirParams } from "../_lib/fhirParams.action";
import { FhirFlowComp } from "./components/fhir-flow";

const redirectUri = "http://localhost:3000/fhir-flow";

// Server component
export default async function FhirFlowPage() {
	const myurls = urls.filter((u) => u.title.toLowerCase().includes("home"));

	const fhirConfiguration = await getFhirParams("local");

	fhirConfiguration.redirectUri = redirectUri;
	fhirConfiguration.pkceMode = "disabled";

	//Just for testing getting other resources
	let fhirResourcesNames: string[] | undefined;

	if (fhirConfiguration.iss) {
		const fclient = new FhirClient(fhirConfiguration.iss);
		try {
			const fhirMeta =
				await fclient.fhirRequest<CapabilityStatement>("metadata");

			if (fhirMeta !== undefined) {
				fhirResourcesNames = fhirMeta.rest?.[0]?.resource?.map(
					(r: { type: string }) => r.type,
				);
			}
		} catch (err) {
			console.error(err);
		}
	}

	return (
		<div>
			<h1>Fhir Flow</h1>
			<p>Uses the local fhir server.</p>
			<ul>
				{myurls.map((item) => (
					<li key={item.url}>
						<Link className="underline" href={item.url}>
							{item.title}
						</Link>
					</li>
				))}
				<FhirFlowComp
					fhirConfiguration={fhirConfiguration}
					fhirResourcesNames={fhirResourcesNames}
				/>
			</ul>
		</div>
	);
}
