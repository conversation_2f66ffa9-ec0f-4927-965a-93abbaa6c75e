import Link from "next/link";
import { urls } from "../_lib/constants";
import { getFhirParams } from "../_lib/fhirParams.action";
import { FhirLaunch } from "./components/fhir-launch";

export type LaunchSearchParams = Promise<{
	[key: string]: string | string[] | undefined;
}>;
export type LaunchSearchParamsNotPromise = {
	[key: string]: string | string[] | undefined;
};

const redirectUri = "http://localhost:3000/fhir-index";

// Server component
export default async function FhirLaunchPage(props: {
	searchParams: LaunchSearchParams;
}) {
	const myurls = urls.filter(
		(u) =>
			u.title.toLowerCase().includes("index") ||
			u.title.toLowerCase().includes("home"),
	);

	const parmas = await props.searchParams;

	const fhirConfiguration = await getFhirParams();
	const localFhirConfiguration = await getFhirParams("local");

	//Check if recived search Params for the iss otherwise use demo iss
	if (parmas?.iss) {
		fhirConfiguration.iss = parmas.iss as string;
	}

	//if launch is provieded externally
	if (parmas?.launch) {
		fhirConfiguration.launch = parmas.launch as string;
	}

	//add this page redirect url
	fhirConfiguration.redirectUri = redirectUri;
	//set patient
	localFhirConfiguration.patientId = "banks-mia-leanne";

	return (
		<div>
			<h1>Fhir client launch</h1>
			<p>
				after launch is clicked will redirect to fhir-index for displaying of
				data.
			</p>
			<div>
				<h4>Here are the deafult fhir configurations</h4>
				<p>{JSON.stringify(fhirConfiguration, null, 2)}</p>
			</div>
			<ul>
				{myurls.map((item) => (
					<li key={item.url}>
						<Link className="underline" href={item.url}>
							{item.title}
						</Link>
					</li>
				))}
			</ul>
			<div className="py-3">
				<div className="text-lg">Launch client</div>
				<div className="flex flex-col gap-y-2">
					<FhirLaunch
						text="launch using local fhir server with (patientId = banks-mia-leanne)"
						fhirConfiguration={localFhirConfiguration}
					/>
					<FhirLaunch
						text="launch using external or searchparams (if set local fhir it will go locally) fhir server"
						fhirConfiguration={fhirConfiguration}
					/>
				</div>
			</div>
		</div>
	);
}
