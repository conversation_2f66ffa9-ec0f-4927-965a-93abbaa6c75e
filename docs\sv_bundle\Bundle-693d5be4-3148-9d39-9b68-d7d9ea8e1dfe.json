{"resourceType": "Bundle", "id": "693d5be4-3148-9d39-9b68-d7d9ea8e1dfe", "meta": {"profile": ["http://hl7.eu/fhir/laboratory/StructureDefinition/Bundle-eu-lab"]}, "type": "transaction", "timestamp": "2023-11-07T11:30:00+01:00", "entry": [{"fullUrl": "urn:uuid:70d7bace-e8dd-d305-3418-ba9439e16d26", "resource": {"resourceType": "Composition", "id": "70d7bace-e8dd-d305-3418-ba9439e16d26", "meta": {"profile": ["http://hl7.eu/fhir/laboratory/StructureDefinition/Composition-eu-lab"]}, "text": {"status": "extensions"}, "extension": [{"url": "http://hl7.eu/fhir/laboratory/StructureDefinition/composition-diagnosticReportReference", "valueReference": {"reference": "DiagnosticReport/7d632709-a5de-b5a7-cbb6-9ad62290096d"}}, {"url": "http://hl7.eu/fhir/StructureDefinition/composition-basedOn-order-or-requisition", "valueReference": {"reference": "ServiceRequest/c7ecf4e6-2f87-f912-00a5-a27fa6e7ce3e", "type": "ServiceRequest"}}], "identifier": {"system": "http://todo.se/ToDo", "value": "**********3"}, "status": "final", "type": {"coding": [{"system": "http://loinc.org", "code": "11502-2", "display": "Laboratory report"}, {"system": "http://snomed.info/sct", "code": "4241000179101", "display": "Laboratorierapport"}, {"system": "urn:oid:2.16.578.********.1.8279", "code": "SVAR_LAB", "display": "Svarrapport-Laboratoriemedisin"}]}, "category": [{"coding": [{"system": "http://loinc.org", "code": "26436-6", "display": "Laboratory Studies (set)"}, {"system": "http://snomed.info/sct", "code": "4241000179101", "display": "Laboratorierapport"}]}], "subject": {"reference": "Patient/5913d24f-d6ca-3130-e08f-5c1a3a719fc4"}, "encounter": {"reference": "Encounter/710071ff-a452-a8e3-1ea9-bc215b39a4e2"}, "date": "2023-11-07T11:30:00+01:00", "author": [{"reference": "PractitionerRole/a6bbbad6-f1a2-8da9-9532-8a908145bf75", "type": "PractitionerRole", "display": "Oldberg Karl"}, {"reference": "Organization/f9f8ad16-36fb-c3ef-de78-3e79412c11be", "type": "Organization", "display": "Klinisk mikrobiologi"}], "title": "Laboratorierapport", "attester": [{"mode": "legal", "time": "2023-11-07T11:06:00+01:00", "party": {"reference": "Organization/f9f8ad16-36fb-c3ef-de78-3e79412c11be", "display": "Klinisk mikrobiologi"}}], "custodian": {"reference": "Organization/f9f8ad16-36fb-c3ef-de78-3e79412c11be", "type": "Organization", "display": "Klinisk mikrobiologi"}, "section": [{"title": "Gruppe", "code": {"coding": [{"system": "urn:oid:2.16.578.********.1.8243", "code": "GR", "display": "Gruppe"}], "text": "Gruppe"}, "entry": [{"reference": "Observation/"}], "section": [{"code": {"coding": [{"system": "urn:oid:2.16.578.********.1.0000", "code": "NPU28898", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>, DNA "}, {"system": "urn:oid:2.16.578.********.1.8212", "code": "DERMATOFYT DNA"}]}, "entry": [{"reference": "Observation/c55094a4-5d26-d897-1727-05282f619e6c", "type": "Observation"}]}]}]}, "request": {"method": "PUT", "url": "Composition/70d7bace-e8dd-d305-3418-ba9439e16d26"}}, {"fullUrl": "urn:uuid:5913d24f-d6ca-3130-e08f-5c1a3a719fc4", "resource": {"resourceType": "Patient", "id": "5913d24f-d6ca-3130-e08f-5c1a3a719fc4", "meta": {"profile": ["http://hl7.eu/fhir/laboratory/StructureDefinition/Patient-eu-lab", "http://bki.skane.se/fhir/historiskalabbdata/StructureDefinition/lab-sd-patient-labbpatient"]}, "text": {"status": "generated"}, "extension": [{"extension": [{"url": "value", "valueCodeableConcept": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/sex-parameter-for-clinical-use", "code": "1", "display": "<PERSON>"}]}}], "url": "http://hl7.org/fhir/StructureDefinition/patient-sexParameterForClinicalUse"}], "identifier": [{"system": "http://electronichealth.se/identifier/personnummer", "value": "19000627-9814"}], "name": [{"text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>"}], "address": [{"use": "home", "line": ["KARLSGATAN 22 LGH 1102"], "city": "LUND", "postalCode": "65222"}]}, "request": {"method": "PUT", "url": "Patient/5913d24f-d6ca-3130-e08f-5c1a3a719fc4"}}, {"fullUrl": "urn:uuid:f39c00a5-cd16-555c-31df-9b7a27d3fbb1", "resource": {"resourceType": "Specimen", "id": "f39c00a5-cd16-555c-31df-9b7a27d3fbb1", "meta": {"profile": ["http://hl7.eu/fhir/laboratory/StructureDefinition/Specimen-eu-lab", "http://bki.skane.se/fhir/historiskalabbdata/StructureDefinition/lab-sd-specimen-labprov"]}, "text": {"status": "generated"}, "accessionIdentifier": {"system": "http://bki.skane.se/fhir/historiskalabbdata/identifiersystem/lims.analysedsubject-by-servprovider", "value": "**********3-001"}, "type": {"text": "<PERSON><PERSON>"}, "subject": {"reference": "Patient/5913d24f-d6ca-3130-e08f-5c1a3a719fc4"}, "collection": {"collectedDateTime": "2023-11-07T11:05:00+01:00", "bodySite": {"text": "Fot, vä"}}, "container": [{"identifier": [{"system": "http://bki.skane.se/fhir/historiskalabbdata/identifiersystem/lims.analysedsubject-by-servrequester", "value": "**********3A"}]}]}, "request": {"method": "PUT", "url": "Specimen/f39c00a5-cd16-555c-31df-9b7a27d3fbb1"}}, {"fullUrl": "urn:uuid:c7ecf4e6-2f87-f912-00a5-a27fa6e7ce3e", "resource": {"resourceType": "ServiceRequest", "id": "c7ecf4e6-2f87-f912-00a5-a27fa6e7ce3e", "meta": {"profile": ["http://hl7.eu/fhir/laboratory/StructureDefinition/ServiceRequest-eu-lab", "http://bki.skane.se/fhir/historiskalabbdata/StructureDefinition/lab-sd-servicerequest-labborder"]}, "text": {"status": "extensions"}, "extension": [{"url": "http://bki.skane.se/fhir/historiskalabbdata/StructureDefinition/receipt-date", "valueDateTime": "2023-11-07T11:29:00+01:00"}], "identifier": [{"system": "http://bki.skane.se/fhir/historiskalabbdata/identifiersystem/lims.servicerequest", "value": "**********3"}, {"system": "http://bki.skane.se/fhir/historiskalabbdata/identifiersystem/lims.servicerequest-by-servprovider", "value": "**********3"}], "status": "unknown", "intent": "order", "category": [{"coding": [{"system": "http://todo.se/ToDo", "code": "MBIO", "display": "Mikrobiologi"}]}], "code": {"coding": [{"system": "http://loinc.org", "code": "14957-5", "display": "[?]Microalbumin <PERSON>/Volume in Urine[?]"}], "text": "[?]Microalbumin <PERSON>/Volume in Urine[?]"}, "subject": {"reference": "Patient/5913d24f-d6ca-3130-e08f-5c1a3a719fc4"}, "authoredOn": "2023-11-07T11:06:00+01:00", "requester": {"reference": "Organization/34cbe6e7-a695-5eed-dd2a-59a9b4e8f6db", "type": "Organization", "identifier": {"system": "http://bki.skane.se/fhir/historiskalabbdata/identifiersystem/lims.requesting-organisation-id", "value": "YSMEVP"}, "display": "Medicinavdelning Ystad"}}, "request": {"method": "PUT", "url": "ServiceRequest/c7ecf4e6-2f87-f912-00a5-a27fa6e7ce3e"}}, {"fullUrl": "urn:uuid:urn:uuid:710071ff-a452-a8e3-1ea9-bc215b39a4e2", "resource": {"resourceType": "Encounter", "id": "710071ff-a452-a8e3-1ea9-bc215b39a4e2", "meta": {"profile": ["http://bki.skane.se/fhir/historiskalabbdata/StructureDefinition/lab-encounter"]}, "text": {"status": "generated"}, "status": "unknown", "class": {"system": "http://terminology.hl7.org/CodeSystem/v3-ActCode", "code": "AMB", "display": "Öppenvårdskontakt"}, "subject": {"reference": "Patient/5913d24f-d6ca-3130-e08f-5c1a3a719fc4", "type": "Patient"}, "basedOn": [{"reference": "ServiceRequest/c7ecf4e6-2f87-f912-00a5-a27fa6e7ce3e", "type": "ServiceRequest"}], "participant": [{"type": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType", "code": "REF", "display": "Remitterande"}]}], "individual": {"reference": "PractitionerRole/64bd6795-54c2-ae7a-67a9-a2fcfa8526a3", "type": "PractitionerRole"}}], "period": {"start": "2023-11-07T11:06:00+01:00"}}, "request": {"method": "PUT", "url": "Encounter/710071ff-a452-a8e3-1ea9-bc215b39a4e2"}}, {"fullUrl": "urn:uuid:7d632709-a5de-b5a7-cbb6-9ad62290096d", "resource": {"resourceType": "DiagnosticReport", "id": "7d632709-a5de-b5a7-cbb6-9ad62290096d", "meta": {"profile": ["http://hl7.eu/fhir/laboratory/StructureDefinition/DiagnosticReport-eu-lab", "http://bki.skane.se/fhir/historiskalabbdata/StructureDefinition/lab-sd-diagnosticreport-diagnostiskrapport"]}, "text": {"status": "generated"}, "identifier": [{"system": "http://todo.se/ToDo", "value": "**********3"}], "status": "final", "category": [{"coding": [{"system": "urn:oid:2.16.578.********.1.8202", "code": "MBIO", "display": "Mikrobiologi"}]}], "code": {"coding": [{"system": "http://bki.skane.se/fhir/todo/CodeSystem/todo", "code": "todo", "display": "att göra"}]}, "effectiveDateTime": "2023-11-07T11:06:00+01:00", "performer": [{"reference": "Organization/f9f8ad16-36fb-c3ef-de78-3e79412c11be", "type": "Organization", "display": "Klinisk mikrobiologi"}, {"reference": "PractitionerRole/a6bbbad6-f1a2-8da9-9532-8a908145bf75", "type": "PractitionerRole", "display": "Oldberg Karl"}], "specimen": [{"reference": "Specimen/f39c00a5-cd16-555c-31df-9b7a27d3fbb1", "type": "Specimen"}], "result": [{"reference": "Observation/c195a6b9-562f-0c69-5867-382db2d4b482", "type": "Observation"}]}, "request": {"method": "PUT", "url": "DiagnosticReport/7d632709-a5de-b5a7-cbb6-9ad62290096d"}}, {"fullUrl": "urn:uuid:c195a6b9-562f-0c69-5867-382db2d4b482", "resource": {"resourceType": "Observation", "id": "c195a6b9-562f-0c69-5867-382db2d4b482", "meta": {"profile": ["http://hl7.eu/fhir/laboratory/StructureDefinition/Observation-eu-lab", "http://bki.skane.se/fhir/historiskalabbdata/StructureDefinition/lab-sd-observation-labbresultat"]}, "text": {"status": "generated"}, "basedOn": [{"reference": "ServiceRequest/c7ecf4e6-2f87-f912-00a5-a27fa6e7ce3e", "type": "ServiceRequest"}], "status": "unknown", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "laboratory", "display": "Laboratory"}]}], "code": {"coding": [{"system": "urn:oid:2.16.578.********.1.8243", "code": "GR", "display": "Gruppe"}], "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, DNA"}, "subject": {"reference": "Patient/5913d24f-d6ca-3130-e08f-5c1a3a719fc4", "type": "Patient"}, "hasMember": [{"reference": "Observation/", "type": "Observation"}]}, "request": {"method": "PUT", "url": "Observation/c195a6b9-562f-0c69-5867-382db2d4b482"}}, {"fullUrl": "urn:uuid:c55094a4-5d26-d897-1727-05282f619e6c", "resource": {"resourceType": "Observation", "id": "c55094a4-5d26-d897-1727-05282f619e6c", "meta": {"profile": ["http://hl7.eu/fhir/laboratory/StructureDefinition/Observation-eu-lab", "http://bki.skane.se/fhir/historiskalabbdata/StructureDefinition/lab-sd-observation-labbresultat"]}, "text": {"status": "generated"}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "laboratory", "display": "Laboratory"}]}], "code": {"coding": [{"system": "urn:oid:2.16.578.********.1.0000", "code": "NPU28898", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>, DNA "}, {"system": "urn:oid:2.16.578.********.1.8212", "code": "DERMATOFYT DNA"}]}, "effectiveDateTime": "2023-11-07T11:30:00+01:00", "valueString": "PÅVISAT", "method": {"coding": [{"system": "http://bki.skane.se/fhir/historiskalabbdata/CodeSystem/accreditation-flag", "code": "accredited"}]}}, "request": {"method": "PUT", "url": "Observation/c55094a4-5d26-d897-1727-05282f619e6c"}}, {"fullUrl": "urn:uuid:f9f8ad16-36fb-c3ef-de78-3e79412c11be", "resource": {"resourceType": "Organization", "id": "f9f8ad16-36fb-c3ef-de78-3e79412c11be", "meta": {"profile": ["http://hl7.se/fhir/ig/base/StructureDefinition/SEBaseOrganization"]}, "text": {"status": "generated"}, "identifier": [{"type": {"coding": [{"system": "urn:oid:2.16.578.********.1.9051"}]}, "system": "http://bki.skane.se/fhir/historiskalabbdata/identifiersystem/lims.serv-organisation-id", "value": "MLU"}], "type": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/organization-type", "code": "prov", "display": "Healthcare Provider"}]}], "name": "Klinisk mikrobiologi"}, "request": {"method": "PUT", "url": "Organization/f9f8ad16-36fb-c3ef-de78-3e79412c11be"}}, {"fullUrl": "urn:uuid:34cbe6e7-a695-5eed-dd2a-59a9b4e8f6db", "resource": {"resourceType": "Organization", "id": "34cbe6e7-a695-5eed-dd2a-59a9b4e8f6db", "meta": {"profile": ["http://hl7.se/fhir/ig/base/StructureDefinition/SEBaseOrganization"]}, "text": {"status": "generated"}, "identifier": [{"type": {"coding": [{"system": "urn:oid:2.16.578.********.1.9051"}]}, "system": "http://bki.skane.se/fhir/historiskalabbdata/identifiersystem/lims.requesting-organisation-id", "value": "YSMEVP"}], "type": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/organization-type", "code": "prov", "display": "Healthcare Provider"}]}], "name": "Medicinavdelning Ystad"}, "request": {"method": "PUT", "url": "Organization/34cbe6e7-a695-5eed-dd2a-59a9b4e8f6db"}}, {"fullUrl": "urn:uuid:a6bbbad6-f1a2-8da9-9532-8a908145bf75", "resource": {"resourceType": "PractitionerRole", "id": "a6bbbad6-f1a2-8da9-9532-8a908145bf75", "meta": {"profile": ["http://bki.skane.se/fhir/historiskalabbdata/StructureDefinition/lab-sd-practitionerrole-behandlarroll", "http://hl7.eu/fhir/base/StructureDefinition/practitionerRole-eu-core"]}, "text": {"status": "generated"}, "active": true, "practitioner": {"display": "Oldberg Karl"}, "organization": {"reference": "Organization/f9f8ad16-36fb-c3ef-de78-3e79412c11be", "display": "Klinisk mikrobiologi"}, "code": [{"coding": [{"system": "http://todo.se/ToDo", "code": "RSP", "display": "Tilknyttet tjenesteyter"}, {"system": "http://todo.se/ToDo", "code": "ALE", "display": "Ansvarlig lege"}]}], "specialty": [{"coding": [{"system": "http://todo.se/ToDo", "code": "LE", "display": "Lege"}]}]}, "request": {"method": "PUT", "url": "PractitionerRole/a6bbbad6-f1a2-8da9-9532-8a908145bf75"}}]}