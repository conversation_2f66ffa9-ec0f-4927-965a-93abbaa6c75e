"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { authClient } from "@/lib/auth-client";

export default function TestAuth() {
	const { data: session } = authClient.useSession();
	const [authRes, setAuthRes] = useState<any>(null);
	const [loading, setLoading] = useState(false);

	const signInSocial = async () => {
		setLoading(true);
		try {
			const res = await authClient.signIn.social({
				provider: "microsoft",
				callbackURL: "/testar",
			});
			console.log("Social sign-in result:", res);
			setAuthRes(res);
		} catch (error) {
			console.error("Social sign-in error:", error);
			setAuthRes({ error: error });
		} finally {
			setLoading(false);
		}
	};

	const signInSSO = async () => {
		setLoading(true);
		try {
			const res = await authClient.signIn.sso({
				domain: "regionskane.onmicrosoft.com",
				callbackURL: "/testar",
			});
			console.log("SSO sign-in result:", res);
			setAuthRes(res);
		} catch (error) {
			console.error("SSO sign-in error:", error);
			setAuthRes({ error: error });
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="space-y-4">
			{!session && (
				<div className="space-y-4">
					<div className="text-lg">Welcome! Please sign in:</div>

					{/* Social Provider Sign-in (Recommended) */}
					<div className="p-4 border rounded-lg bg-green-50">
						<h3 className="font-semibold text-green-800 mb-2">
							✅ Social Provider (Working)
						</h3>
						<p className="text-sm text-green-700 mb-3">
							This uses your configured Microsoft social provider and should
							work immediately.
						</p>
						<Button
							variant="default"
							onClick={signInSocial}
							disabled={loading}
							className="bg-green-600 hover:bg-green-700"
						>
							{loading ? "Signing in..." : "Sign in with Microsoft (Social)"}
						</Button>
					</div>

					{/* SSO Sign-in (Requires Setup) */}
					<div className="p-4 border rounded-lg bg-yellow-50">
						<h3 className="font-semibold text-yellow-800 mb-2">
							⚠️ SSO (Requires Database Setup)
						</h3>
						<p className="text-sm text-yellow-700 mb-3">
							This requires SSO provider configuration in the database first.
						</p>
						<Button
							variant="outline"
							onClick={signInSSO}
							disabled={loading}
							className="border-yellow-600 text-yellow-800 hover:bg-yellow-100"
						>
							{loading ? "Signing in..." : "Sign in with SSO (Domain-based)"}
						</Button>
					</div>
				</div>
			)}

			{session && (
				<div className="p-4 border rounded-lg bg-blue-50">
					<div className="text-lg text-blue-800 mb-2">
						✅ Signed in as:{" "}
						<strong>{session.user.name || session.user.email}</strong>
					</div>
					<div className="text-sm text-blue-600 mb-3">
						User ID: {session.user.id}
					</div>
					<Button variant="destructive" onClick={() => authClient.signOut()}>
						Sign out
					</Button>
				</div>
			)}

			{/* Debug Information */}
			{authRes && (
				<div className="p-4 border rounded-lg bg-gray-50">
					<h4 className="font-semibold mb-2">Last Authentication Result:</h4>
					<pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
						{JSON.stringify(authRes, null, 2)}
					</pre>
				</div>
			)}
		</div>
	);
}
