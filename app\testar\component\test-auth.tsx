"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { authClient } from "@/lib/auth-client";

export default function TestAuth() {
	const { data: session } = authClient.useSession();
	const [ssoRes, setSsoRes] = useState<any>(null);

	const _signInSSO = async () => {
		const res = await authClient.signIn.sso({
			domain: "example.com",
			callbackURL: "/dashboard",
		});
		console.log(res);
		setSsoRes(res);
	};

	return (
		<div>
			{!session && (
				<div className="flex gap-y-3 flex-col">
					<div>hello</div>
					<button
						type="button"
						className="bg-blue-400 p-2 w-fit active:bg-blue-500 cursor-pointer"
						onClick={() => authClient.signIn.social({ provider: "microsoft" })}
					>
						Sign in Social with microsoft
					</button>
					<div>
						<Button variant="default" type="button">
							Sign in SSO
						</Button>
					</div>
				</div>
			)}
			{session && (
				<div>
					<div>hello {session.user.name}</div>
					<button
						type="button"
						className="bg-blue-400 p-2 w-fit active:bg-blue-500 cursor-pointer"
						onClick={() => authClient.signOut()}
					>
						Sign out
					</button>
				</div>
			)}
			<div>
				<div>res:</div>
				<div>{JSON.stringify(ssoRes, null, 2)}</div>
			</div>
		</div>
	);
}
