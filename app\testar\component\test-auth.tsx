"use client";

import { authClient } from "@/lib/auth-client";

export default function TestAuth() {
	const { data: session } = authClient.useSession();

	return (
		<div>
			{!session && (
				<div>
					<div>hello</div>
					<button
						type="button"
						className="bg-blue-400 p-2 w-fit active:bg-blue-500 cursor-pointer"
						onClick={() => authClient.signIn.social({ provider: "microsoft" })}
					>
						Sign in with microsoft
					</button>
				</div>
			)}
			{session && (
				<div>
					<div>hello {session.user.name}</div>
					<button
						type="button"
						className="bg-blue-400 p-2 w-fit active:bg-blue-500 cursor-pointer"
						onClick={() => authClient.signOut()}
					>
						Sign out
					</button>
				</div>
			)}
		</div>
	);
}
