{"name": "lab-data-smart-fhir-client", "version": "0.0.5", "private": true, "engines": {"node": ">=22.0.0"}, "scripts": {"dev": "next dev", "build": "next build --turbopack", "start": "next start", "lint": "biome check", "format": "biome format --write", "test": "vitest", "test:coverage": "vitest run --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "e2e": "pnpm exec playwright test", "setup-sso": "tsx scripts/setup-sso-provider.ts", "typecheck": "tsc --noEmit", "check": "pnpm biome check", "check:w": "pnpm biome check --write", "changeset": "changeset", "changeset:version": "changeset version", "better:gen": "pnpm dlx @better-auth/cli generate", "better:driz": "pnpm exec drizzle-kit generate", "better:mig": "pnpm exec drizzle-kit migrate"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@libsql/client": "^0.15.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@swc/helpers": "^0.5.17", "better-auth": "^1.3.11", "better-sqlite3": "^12.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.44.5", "fhirclient": "^2.6.0", "lucide-react": "^0.544.0", "next": "15.5.3", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "zod": "^4.1.8"}, "devDependencies": {"@biomejs/biome": "2.2.4", "@changesets/cli": "^2.29.7", "@eslint/eslintrc": "^3.3.1", "@playwright/test": "^1.55.0", "@storybook/addon-docs": "9.1.6", "@storybook/addon-onboarding": "9.1.6", "@storybook/nextjs": "9.1.6", "@tailwindcss/postcss": "^4.1.13", "@testing-library/dom": "^10.4.1", "@testing-library/react": "^16.3.0", "@types/better-sqlite3": "^7.6.13", "@types/fhir": "^0.0.41", "@types/node": "^22.18.4", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.2", "@vitest/coverage-v8": "^3.2.4", "dotenv": "^17.2.2", "drizzle-kit": "^0.31.4", "eslint-plugin-storybook": "^9.1.6", "jsdom": "^27.0.0", "storybook": "9.1.6", "tailwindcss": "^4.1.13", "tsx": "^4.20.5", "tw-animate-css": "^1.3.8", "typescript": "^5.9.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "packageManager": "pnpm@10.15.1+sha512.34e538c329b5553014ca8e8f4535997f96180a1d0f614339357449935350d924e22f8614682191264ec33d1462ac21561aff97f6bb18065351c162c7e8f6de67"}