"use server";

import { auth } from "@/lib/auth";

export const signIn = async (email: string, password: string) => {
  // const res = await auth.api.signIn.sso({
  // 	email: "<EMAIL>",
  // 	callbackURL: "/",
  // });
  // console.log("res", res);

  try {
    await auth.api.signInEmail({
      body: {
        email,
        password
      }
    });
    console.log("signed in");
    return {
      success: true,
      message: "signed in"
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: (error as Error).message || "failed to login"
    };
  }


};

export const signUp = async () => {
  await auth.api.signUpEmail({
    body: {
      email: "<EMAIL>",
      password: "12345678",
      name: "Test User",
    }
  });
};
