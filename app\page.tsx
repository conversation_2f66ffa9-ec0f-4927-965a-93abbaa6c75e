import Link from "next/link";
import { authClient } from "@/lib/auth-client";
import { urls } from "./_lib/constants";

export default async function Home() {
	const res = await authClient.signIn.sso({
		email: "<EMAIL>",
		callbackURL: "/",
	});
	console.log("res", res);

	return (
		<div className="grid items-center justify-center">
			<h1 className="text-3xl py-2">Home</h1>
			<ul className="space-y-1">
				{urls.map((item) => {
					return (
						<li key={item.url}>
							<Link className="underline" href={item.url}>
								{item.title}
							</Link>
						</li>
					);
				})}
			</ul>
			<div>
				<div>Testing auth with better-auth</div>
				{/* <TestAuth /> */}
			</div>
		</div>
	);
}
