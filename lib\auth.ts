import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { nextCookies } from "better-auth/next-js";
import { sso } from "better-auth/plugins/sso";
import { db } from "@/db";
import { schema } from "@/db/schema/auth-schema";

// export const auth = betterAuth({
//     database: drizzleAdapter(db, {
//         provider: "sqlite", // or "mysql", "sqlite"
//     }),
// });

export const auth = betterAuth({
  appName: "better-labdata",
  emailAndPassword: {
    enabled: true,
  },
  database: drizzleAdapter(db, {
    provider: "sqlite", // or "mysql", "sqlite"
    schema: schema,
  }),
  socialProviders: {
    // github: {
    //   clientId: process.env.AUTH_GITHUB_ID as string,
    //   clientSecret: process.env.AUTH_GITHUB_SECRET as string,
    // },
    microsoft: {
      clientId: process.env.AUTH_AZURE_AD_ID as string,
      clientSecret: process.env.AUTH_AZURE_AD_SECRET as string,

      // Optional
      tenantId: process.env.AUTH_AZURE_AD_TENANT_ID as string,
      prompt: "select_account", // Forces account selection
      issuer: "https://login.microsoftonline.com/common" as string,
    },
  },
  plugins: [sso(), nextCookies()], //nextCookies() need to be last
});

//http://localhost:3000/api/auth/callback/microsoft
