import { headers } from "next/headers";
import Link from "next/link";
import { LogoutBtn } from "@/components/logout";
import { auth } from "@/lib/auth";
import { urls } from "../_lib/constants";
export default async function DashboardPage() {
	const session = await Promise.all([
		auth.api.getSession({
			headers: await headers(),
		}),
		auth.api.listSessions({
			headers: await headers(),
		}),
		// auth.api.listDeviceSessions({
		// 	headers: await headers(),
		// }),
		// auth.api.getFullOrganization({
		// 	headers: await headers(),
		// }),
		// auth.api.listActiveSubscriptions({
		// 	headers: await headers(),
		// }),
	]).catch((e) => {
		console.log(e);
		// throw redirect("/login");
	});
	return (
		<div className="w-full">
			{urls.map((item) => {
				return (
					<li key={item.url}>
						<Link className="underline" href={item.url}>
							{item.title}
						</Link>
					</li>
				);
			})}
			<div className="flex gap-4 flex-col">
				<div>This is the dashboard</div>
				<LogoutBtn />

				<div>
					current session:{" "}
					{session
						? JSON.stringify(session, null, 2)
						: "No session, not logged in"}
				</div>
			</div>
		</div>
	);
}
