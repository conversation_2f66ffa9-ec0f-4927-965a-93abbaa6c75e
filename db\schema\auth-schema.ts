import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";

export const user = sqliteTable("user", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  emailVerified: integer("email_verified", { mode: "boolean" })
    .default(false)
    .notNull(),
  image: text("image"),
  createdAt: integer("created_at", { mode: "timestamp" })
    .defaultNow()
    .notNull(),
  updatedAt: integer("updated_at", { mode: "timestamp" })
    .defaultNow()
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
});

export const session = sqliteTable("session", {
  id: text("id").primaryKey(),
  expiresAt: integer("expires_at", { mode: "timestamp" }).notNull(),
  token: text("token").notNull().unique(),
  createdAt: integer("created_at", { mode: "timestamp" })
    .defaultNow()
    .notNull(),
  updatedAt: integer("updated_at", { mode: "timestamp" })
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
});

export const account = sqliteTable("account", {
  id: text("id").primaryKey(),
  accountId: text("account_id").notNull(),
  providerId: text("provider_id").notNull(),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  accessToken: text("access_token"),
  refreshToken: text("refresh_token"),
  idToken: text("id_token"),
  accessTokenExpiresAt: integer("access_token_expires_at", {
    mode: "timestamp",
  }),
  refreshTokenExpiresAt: integer("refresh_token_expires_at", {
    mode: "timestamp",
  }),
  scope: text("scope"),
  password: text("password"),
  createdAt: integer("created_at", { mode: "timestamp" })
    .defaultNow()
    .notNull(),
  updatedAt: integer("updated_at", { mode: "timestamp" })
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
});

export const verification = sqliteTable("verification", {
  id: text("id").primaryKey(),
  identifier: text("identifier").notNull(),
  value: text("value").notNull(),
  expiresAt: integer("expires_at", { mode: "timestamp" }).notNull(),
  createdAt: integer("created_at", { mode: "timestamp" })
    .defaultNow()
    .notNull(),
  updatedAt: integer("updated_at", { mode: "timestamp" })
    .defaultNow()
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
});

export const ssoProvider = sqliteTable("sso_provider", {
  id: text("id").primaryKey(),
  issuer: text("issuer").notNull(),
  oidcConfig: text("oidc_config"),
  samlConfig: text("saml_config"),
  userId: text("user_id").references(() => user.id, { onDelete: "cascade" }),
  providerId: text("provider_id").notNull().unique(),
  organizationId: text("organization_id"),
  domain: text("domain").notNull(),
});

async function setupSSOProvider() {
  try {
    console.log("Setting up SSO provider for regionskane.onmicrosoft.com...");
    
    // Insert SSO provider configuration
    await db.insert(schema.ssoProvider).values({
      id: "sso-regionskane-" + Date.now(),
      issuer: process.env.AUTH_AZURE_AD_ISSUER || "https://login.microsoftonline.com/92f52389-3f0f-4623-9a3b-957c32d194e5/v2.0",
      domain: "regionskane.onmicrosoft.com",
      providerId: "microsoft-sso-regionskane",
      oidcConfig: JSON.stringify({
        clientId: process.env.AUTH_AZURE_AD_ID,
        clientSecret: process.env.AUTH_AZURE_AD_SECRET,
        tenantId: process.env.AUTH_AZURE_AD_TENANT_ID,
      }),
      organizationId: null,
      userId: null,
    });

    console.log("✅ SSO provider setup completed!");
    console.log("You can now use authClient.signIn.sso({ domain: 'regionskane.onmicrosoft.com' })");
    
  } catch (error) {
    console.error("❌ Error setting up SSO provider:", error);
  }
}

// Run the setup if this file is executed directly
if (require.main === module) {
  setupSSOProvider().then(() => process.exit(0));
}

export { setupSSOProvider };

export const schema = { user, session, account, verification, ssoProvider };
