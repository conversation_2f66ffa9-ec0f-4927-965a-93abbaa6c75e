/**
 * <PERSON><PERSON><PERSON> to set up SSO provider in the database for domain-based authentication
 * Run this script to enable SSO login with domain "regionskane.onmicrosoft.com"
 */

import { db } from "../db";
import { schema } from "../db/schema/auth-schema";

async function setupSSOProvider() {
  try {
    console.log("Setting up SSO provider for regionskane.onmicrosoft.com...");
    
    // Insert SSO provider configuration
    await db.insert(schema.ssoProvider).values({
      id: "sso-regionskane-" + Date.now(),
      issuer: process.env.AUTH_AZURE_AD_ISSUER || "https://login.microsoftonline.com/92f52389-3f0f-4623-9a3b-957c32d194e5/v2.0",
      domain: "regionskane.onmicrosoft.com",
      providerId: "microsoft-sso-regionskane",
      oidcConfig: JSON.stringify({
        clientId: process.env.AUTH_AZURE_AD_ID,
        clientSecret: process.env.AUTH_AZURE_AD_SECRET,
        tenantId: process.env.AUTH_AZURE_AD_TENANT_ID,
      }),
      organizationId: null,
      userId: null,
    });

    console.log("✅ SSO provider setup completed!");
    console.log("You can now use authClient.signIn.sso({ domain: 'regionskane.onmicrosoft.com' })");
    
  } catch (error) {
    console.error("❌ Error setting up SSO provider:", error);
  }
}

// Run the setup if this file is executed directly
if (require.main === module) {
  setupSSOProvider().then(() => process.exit(0));
}

export { setupSSOProvider };
