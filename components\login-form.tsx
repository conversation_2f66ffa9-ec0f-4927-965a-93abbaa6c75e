"use client";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { authClient } from "@/lib/auth-client";
import { cn } from "@/lib/utils";
import { signIn } from "@/server/users";

const formSchema = z.object({
	email: z.email(),
	password: z.string().min(8),
});

export function LoginForm({
	className,
	...props
}: React.ComponentProps<"div">) {
	const _router = useRouter();
	const [_isLoading, setIsLoading] = useState(false);
	const [loginState, _setLoginState] = useState({
		message: "",
		success: false,
	});
	// 1. Define your form.
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			email: "",
			password: "",
		},
	});

	const signInWithMS = async () => {
		try {
			await authClient.signIn.social({
				provider: "microsoft",
				callbackURL: "/dashboard",
			});
		} catch (error) {
			console.error(error);
		}
	};

	// 2. Define a submit handler.
	async function onSubmit(values: z.infer<typeof formSchema>) {
		// Do something with the form values.
		// ✅ This will be type-safe and validated.
		setIsLoading(true);
		console.log(values);
		const signState = await signIn(values.email, values.password);
		_setLoginState(signState);
		if (signState.success) {
			_router.push("/dashboard");
		}
		setIsLoading(false);
	}

	return (
		<div className={cn("flex flex-col gap-6", className)} {...props}>
			<div>Sign in status: {loginState.message}</div>
			<div>Sign in status: {loginState.success ? "success" : "failed"}</div>
			<Card>
				<CardHeader className="text-center">
					<CardTitle className="text-xl">Welcome back</CardTitle>
					<CardDescription>Login with your Microsoft account</CardDescription>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)}>
							<div className="grid gap-6">
								<div className="flex flex-col gap-4">
									<Button
										type="button"
										variant="outline"
										className="w-full"
										onClick={signInWithMS}
									>
										Login with Microsoft
									</Button>
								</div>
								<div className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
									<span className="bg-card text-muted-foreground relative z-10 px-2">
										Or continue with
									</span>
								</div>
								<div className="grid gap-6">
									<div className="grid gap-3">
										<FormField
											control={form.control}
											name="email"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Email</FormLabel>
													<FormControl>
														<Input placeholder="<EMAIL>" {...field} />
													</FormControl>

													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
									<div className="grid gap-3">
										<FormField
											control={form.control}
											name="password"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Password</FormLabel>
													<FormControl>
														<Input type="password" {...field} />
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
									<Button
										type="submit"
										className="w-full"
										disabled={_isLoading}
									>
										Login
									</Button>
								</div>
								{/* <div className="text-center text-sm">
									Don&apos;t have an account?{" "}
									<a href="#" className="underline underline-offset-4">
										Sign up
									</a>
								</div> */}
							</div>
						</form>
					</Form>
				</CardContent>
			</Card>
			{/* <div className="text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4">
				By clicking continue, you agree to our <a href="#">Terms of Service</a>{" "}
				and <a href="#">Privacy Policy</a>.
			</div> */}
		</div>
	);
}
