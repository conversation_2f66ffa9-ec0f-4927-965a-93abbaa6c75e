import { authClient } from "@/lib/auth-client";

await authClient.signIn.social({
  /**
   * The social provider ID
   * @example "github", "google", "apple"
   */
  provider: "microsoft",
  /**
   * A URL to redirect after the user authenticates with the provider
   * @default "/"
   */
  callbackURL: "/testar",
  // callbackURL: "/dashboard",
  /**
   * A URL to redirect if an error occurs during the sign in process
   */
  errorCallbackURL: "/testar",
  // errorCallbackURL: "/error",
  /**
   * A URL to redirect if the user is newly registered
   */
  newUserCallbackURL: "/testar",
  // newUserCallbackURL: "/welcome",
  /**
   * disable the automatic redirect to the provider.
   * @default false
   */
  disableRedirect: true,
});
